import re
from typing import Literal
from sugon_web.pages.login import LoginPage
from sugon_web.common.elements import CCEElements


class CCEPage(LoginPage):
    def __init__(self, page, env):
        super().__init__(page, env)

    def navigate_to_cce(self):
        """导航到CCE页面"""
        self._ensure_login(name=self.env["username"], pwd=self.env["password"])
        self.navigate_to_service('云容器引擎')

    def cce_create(
            self,
            name,
            version: Literal["1.20.4", "1.22.17"] = "1.22.17",
            count=5,
            runtime: Literal["docker", "containerd"] = "docker",
            proxy: Literal["ipvs", "iptables"] = "ipvs",
            az="Autotest",
            vpc="Autotest",
            net_mode: Literal["calico", "flannel"] = "flannel",
            volume_type="xstor-type",
            volume_size=50,
            os_type="AnolisOS 8.6"
            ):

        self.click(CCEElements.cluster_create)

        # 基本信息
        self.fill(CCEElements.name, name)
        self.fill(CCEElements.node_count, count)
        self._select_version(version)
        self.click(runtime)
        self.click(proxy)
        self._select_az(az)

        # 网络设置
        self._select_vpc(vpc)
        self._select_network_mode(net_mode)

        # 其它配置
        self._select_volume_type(volume_type)
        self.fill(CCEElements.volume_size, volume_size)
        self._select_os_type(os_type)
        self.click(CCEElements.flavor)   # todo：规格已写死
        self.click(CCEElements.confirm)

    def _select_version(self, version):
        """选择CCE版本"""
        self.click(CCEElements.version)
        self.click(version)

    def _select_az(self, az_name):
        """选择可用区选项"""
        self.click(CCEElements.az)
        az_element = self.locator("ul").filter(has_text=re.compile(rf"^{az_name}$")).locator("span")
        az_element.click()

    def _select_vpc(self, vpc_name):
        """选择VPC选项"""
        self.click(CCEElements.vpc)
        vpc_element = self.locator("li").filter(has_text=vpc_name)
        vpc_element.click()
        # self.click(CCEPage.subnet)
        # self.locator("span").filter(has_text="Autotest:").click()

    def _select_network_mode(self, net_mode):
        """选择网络模式"""
        self.click(CCEElements.net_type)
        net_element = self.locator("li").filter(has_text=re.compile(rf"^{net_mode}$"))
        net_element.click()

    def _select_volume_type(self, volume_type):
        """选择云硬盘类型"""
        self.click(CCEElements.volume_type)
        self.click(volume_type)

    def _select_os_type(self, os_type):
        """选择操作系统类型"""
        self.click(CCEElements.os_type)
        self.click(os_type)


