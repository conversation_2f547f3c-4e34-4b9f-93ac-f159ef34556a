import re
from sugon_web.pages.login import LoginPage
from sugon_web.common.elements import CCEPage


class CCE(LoginPage):
    def __init__(self, page, env):
        super().__init__(page, env)

    def navigate_to_cce(self):
        """导航到CCE页面"""
        self._ensure_login(name=self.env["username"], pwd=self.env["password"])
        self.navigate_to_service('云容器引擎')

    def cce_create(
            self,
            name,
            version="1.22.17",
            count=5,
            az="Autotest",
            vpc="Autotest",
            net_mode="calico",
            volume_type="xstor-type",
            volume_size=50,
            os_type="AnolisOS 8.6"
            ):

        self.click(CCEPage.btn_create)

        self.fill(CCEPage.ipt_name, name)

        self.click(CCEPage.drop_version)
        self.click(version)

        self.click(CCEPage.drop_az)
        self.locator("ul").filter(has_text=re.compile(rf"^{az}$")).locator("span").click()

        # self.get_by_placeholder("请选择网络").click()
        self.click(CCEPage.drop_vpc)
        self.locator("li").filter(has_text=vpc).click()  # todo:下拉框数据量大，考虑实现滑动选择
        # self.get_by_placeholder("请选择子网").click()
        # self.locator("span").filter(has_text="Autotest:").click()

        self.click(CCEPage.drop_net_type)
        self.locator("li").filter(has_text=net_mode).click()

        self.click(CCEPage.drop_disk_type)
        self.get_by_text(volume_type).click()

        self.fill(CCEPage.ipt_disk_size, str(volume_size))

        self.click(CCEPage.drop_os_type)
        self.click(os_type)

        self.click(CCEPage.radio_flavor)   # todo：规格已写死
        self.click(CCEPage.btn_confirm)


