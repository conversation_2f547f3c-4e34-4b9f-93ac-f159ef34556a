import re
from sugon_web.pages.login import LoginPage
from sugon_web.common.elements import CreatePage


class CCEPage(LoginPage):

    def __init__(self, page, env):
        super().__init__(page, env)  # 调用父类构造方法
        self._ensure_login(name=env["username"], pwd=env["password"])
        self.navigate_to_service('云容器引擎')

    def cce_create(
            self,
            name,
            version="1.22.17",
            count=5,
            az="Autotest",
            vpc="Autotest",
            net_mode="calico",
            volume_type="xstor-type",
            volume_size=50,
            os_type="AnolisOS 8.6"
            ):

        self.click('创建集群')

        self.get_by_placeholder("(2~50位字符)").fill(name) # no log

        self.click(CreatePage.drop_version)
        self.click(version)

        self.click(CreatePage.drop_az)
        self.locator("ul").filter(has_text=re.compile(rf"^{az}$")).locator("span").click()  # no log

        self.get_by_placeholder("请选择网络").click()    # no log
        self.locator("li").filter(has_text=vpc).click()  # todo:下拉框数据量大，考虑实现滑动选择 # no log
        # self.get_by_placeholder("请选择子网").click()
        # self.locator("span").filter(has_text="Autotest:").click()

        self.click(CreatePage.drop_net_type)
        self.locator("li").filter(has_text=net_mode).click()    # no log

        self.click(CreatePage.drop_disk_type)
        self.get_by_text(volume_type).click()

        self.fill(CreatePage.ipt_disk_size, str(volume_size))

        self.click(CreatePage.drop_os_type)
        self.click(os_type)

        self.click(CreatePage.radio_flavor)   # todo：规格已写死
        self.click('立即创建')


