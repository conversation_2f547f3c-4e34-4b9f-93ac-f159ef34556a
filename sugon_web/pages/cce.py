import re
from typing import Literal
from sugon_web.pages.login import LoginPage
from sugon_web.common.elements import CCEElements


class CCEPage(LoginPage):
    def __init__(self, page, env):
        super().__init__(page, env)

    def navigate_to_cce(self):
        """导航到CCE页面"""
        self._ensure_login(name=self.env["username"], pwd=self.env["password"])
        self.navigate_to_service('云容器引擎')

    def cce_create(
            self,
            name,
            version: Literal["1.20.4", "1.22.17"] = "1.22.17",
            count=5,
            runtime: Literal["docker", "containerd"] = "docker",
            proxy: Literal["ipvs", "iptables"] = "ipvs",
            az="Autotest",
            vpc="Autotest",
            net_mode: Literal["calico", "flannel"] = "flannel",
            volume_type="xstor-type",
            volume_size=50,
            os_type="AnolisOS 8.6"
            ):

        self.click(CCEElements.cluster_create)

        # 基本信息
        self.fill(CCEElements.name, name)
        self.fill(CCEElements.node_count, count)

        self._select_version(version)
        self._select_runtime(runtime)
        self.get_by_text(proxy).click()
        self._select_az(az)

        # 网络设置
        self.click(CCEElements.vpc)
        self.locator("li").filter(has_text=vpc).click()
        # self.click(CCEPage.subnet)
        # self.locator("span").filter(has_text="Autotest:").click()
        self.click(CCEElements.net_type)
        self.locator("li").filter(has_text=re.compile(rf"^{net_mode}$")).click()

        # 其它配置
        self.click(CCEElements.disk_type)
        self.get_by_text(volume_type).click()
        self.fill(CCEElements.disk_size, volume_size)
        self.click(CCEElements.os_type)
        self.click(os_type)
        self.click(CCEElements.flavor)   # todo：规格已写死
        self.click(CCEElements.confirm)

    def _select_version(self, version):
        """选择CCE版本"""
        self.click(CCEElements.version)
        self.click(version)

    def _select_runtime(self, runtime):
        """选择容器运行时"""
        self.get_by_text(runtime).click()

    def _select_az(self, az_name):
        """选择可用区选项"""
        self.click(CCEElements.az)
        az_element = self.locator("ul").filter(has_text=re.compile(rf"^{az_name}$")).locator("span")
        az_element.click()


