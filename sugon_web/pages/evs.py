import re
from sugon_web.pages.login import LoginPage
from sugon_web.common.elements import CommonPage


class EvsPage(LoginPage):

    def __init__(self, page, env):
        super().__init__(page, env)  # 调用父类构造方法
        self.login(name=env["username"], pwd=env["password"])
        self.close_license_popup_if_exists()

    def close_license_popup_if_exists(self):
        """如果出现平台许可到期弹窗，则点击确定关闭"""
        try:
            self.page.get_by_role("button", name="确定").click(timeout=5000)
        except Exception:
            # 没有弹窗时不做任何处理
            pass

    def navigate_to_evs(self, menu=None):
        """导航到云硬盘页面"""
        self.page.hover('"资源中心"')
        self.page.hover('"存储"')
        self.page.click('"云硬盘"')
        if menu:
            self.page.click(menu)

    def search(self, keyword: str):
        """执行搜索操作"""
        self.page.locator(CommonPage.ipt_search).fill(keyword)
        self.page.locator(CommonPage.btn_search).click()