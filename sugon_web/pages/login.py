from playwright.sync_api import Locator
from sugon_web.common.base import BasePage

class LoginPage(BasePage):

    def __init__(self, page, env):
        super().__init__(page, env)  # 调用父类构造方法

    def login(self, name: str, pwd: str):
        """执行登录操作"""
        self._input_username.fill(name)
        self._input_password.fill(pwd)
        self._btn_login.click()

    @property
    def _input_username(self) -> Locator:
        return self.get_by_placeholder("请输入登录账号")

    @property
    def _input_password(self) -> Locator:
        return self.get_by_placeholder("请输入登录密码")

    @property
    def _btn_login(self) -> Locator:
        return self.get_by_text("登 录")

    def _is_logged_in(self):
        """检查是否已登录"""
        try:
            # 如果能找到登录表单，说明未登录
            self._input_username.wait_for(timeout=2000)
            return False
        except:
            # 找不到登录表单，说明已登录
            return True

    def _ensure_login(self, name: str, pwd: str):
        """确保已登录，如果未登录则执行登录"""
        if not self._is_logged_in():
            self.login(name, pwd)
            self.close_dialog_if_exists()

