import re
import pytest
from playwright.sync_api import Page, expect


@pytest.fixture(scope="session")
def browser_context_args(browser_context_args, playwright):
    return {"ignore_https_errors": True}


@pytest.fixture(scope="function", autouse=True)
def before_each_after_each(page: Page):
    print("before the test runs")

    # Go to the starting url before each test.
    page.goto("https://172.22.1.170:30000")

    # input的定位方法
    input_name = page.get_by_placeholder("请输入登录账号")
    input_name.fill("admin")
    input_pwd = page.get_by_placeholder("请输入登录密码")
    input_pwd.fill("keystone_sugon")

    page.click('"登 录"')

    yield page

    print("after the test runs")


def test_login_user_error(page: Page):
    """用户登录-用户名输入错误"""
    page.goto("https://172.22.1.170:30000")
    input_name = page.get_by_placeholder("请输入登录账号")
    input_name.fill("wrong_user")
    input_pwd = page.get_by_placeholder("请输入登录密码")
    input_pwd.fill("wrong_pwd")
    page.click('"登 录"')
    expect(page.locator(".el-message__content")).to_contain_text("用户名/密码错误")
    expect(page.locator(".el-message__content")).not_to_be_visible()


def test_cce_create(page: Page):

    # 进入cce服务菜单
    page.click('"资源中心"')
    page.click('"容器"')
    page.click('"云容器引擎"')
    page.click('"集群管理"')
    # 名称输入框
    page.click('"创建集群"')
    page.get_by_placeholder("(2~50位字符)").fill("cce-test")
    # 版本下拉框
    page.click('(//input[@type="text"])[6]')
    page.click('"1.22.17"')
    # 集群下拉框
    page.click('(//input[@type="text"])[7]')
    page.locator("ul").filter(has_text=re.compile(r"^Autotest$")).locator("span").click()
    # 专有网络下拉框
    page.get_by_placeholder("请选择网络").click()
    page.get_by_text("Autotest").nth(2).click()
    page.get_by_placeholder("请选择子网").click()
    page.locator("span").filter(has_text="Autotest:").click()
    # 容器网络模型下拉框
    page.click('.el-form-item:nth-child(2) .el-select__caret')
    page.locator("li").filter(has_text="calico").click()
    # 云硬盘类型下拉框
    page.click('(//input[@type="text"])[29]')
    page.get_by_text("xstor-test").click()
    # 云硬盘大小输入框
    page.fill('(//input[@type="text"])[30]', "1000")
    # 操作系统下拉框
    page.click('(//input[@type="text"])[31]')
    page.click('"AnolisOS 8.6"')
    # 规格单选框
    page.click('.el-table__fixed-body-wrapper .el-table__row:nth-child(1) .el-radio__inner')
    page.click('"立即创建"')

