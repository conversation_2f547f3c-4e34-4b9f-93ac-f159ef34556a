import re
from sugon_web.utils.assertion import assert_table_contain, assert_status, assert_popup_success


class TestEVS:

    def test_volume_create(self, evs_page):
        """云硬盘创建功能验证"""

        evs_page.locator("div").filter(has_text=re.compile(r"^云硬盘$")).nth(1).click()

        name = "volume-test"
        evs_page.evs_create(name, desc="1235")

        assert_popup_success(evs_page)
        assert_status(evs_page, name, status="可用")

    def test_volume_search(self, evs_page):
        """云硬盘页面列表搜索结果验证"""

        evs_page.locator("div").filter(has_text=re.compile(r"^云硬盘$")).nth(2).click()

        keyword = "volume-test"
        evs_page.btn_search(keyword)

        assert_table_contain(evs_page, keyword)


class TestEVSS:

    pass
