"""
定位元素的变量命名前缀规则如下：
按钮：btn_*
输入框：ipt_*
下拉框：drop_*
选择框：select_*
单选框：radio_*
"""


class BaseElements:

    # 页面弹窗
    popup = ".el-message__content"
    # 页面列表搜索框
    ipt_search = ".input-with-select > .el-input__inner"
    # 搜索按钮
    btn_search = '"搜索"'
    # 服务导航映射表 - 支持不同层级结构
    SERVICE_NAVIGATION_MAP = {
        # 资源中心 -> 二级菜单 -> 服务 (三层结构)

        # 计算服务
        '云服务器': ('资源中心', '计算'),
        '裸金属': ('资源中心', '计算'),

        # 网络服务
        '虚拟私有云': ('资源中心', '网络'),
        '云防火墙': ('资源中心', '网络'),

        # 存储服务
        '云硬盘': ('资源中心', '存储'),
        '对象存储': ('资源中心', '存储'),
        '文件存储': ('资源中心', '存储'),

        # 容器服务
        '云容器引擎': ('资源中心', '容器'),
        '应用市场': ('资源中心', '容器'),
        '容器镜像服务': ('资源中心', '容器'),
        '服务治理': ('资源中心', '容器'),

        # 数据库服务
        'AnhanDB(for MySQL)': ('资源中心', '数据库'),
        'AnhanDB(for PostgreSQL)': ('资源中心', '数据库'),
        'AnhanDB(for MongoDB)': ('资源中心', '数据库'),
        '数据仓库 Doris': ('资源中心', '数据库'),

        # 中间件服务
        'AnhanDB(for Redis)': ('资源中心', '中间件'),
        '分布式消息服务 Kafka': ('资源中心', '中间件'),
        '分布式消息服务 RabbitMQ': ('资源中心', '中间件'),
        '云搜索服务': ('资源中心', '中间件'),
        '监控服务': ('资源中心', '中间件'),

        # 基础设施 -> 服务 (两层结构)
        '区域资源': ('基础设施',),
        '计算设施': ('基础设施',),
        '网络设施': ('基础设施',),
        '存储设施': ('基础设施',),
        '备份设施': ('基础设施',),

        # 运维 -> 服务 (两层结构)
        '监控': ('运维',),
        '告警': ('运维',),
        '消息日志': ('运维',),
        '智能搜索': ('运维',),
        '一键巡检': ('运维',),
        '平台升级': ('运维',),
    }


class CCEElements(BaseElements):

    # 创建页面元素
    name = '[placeholder="(2~50位字符)"]'
    node_count = '(//input[@type="text"])[5]'   # 集群节点数量
    version = '(//input[@type="text"])[6]'   # CCE版本下拉框
    az = '(//input[@type="text"])[7]'   # 集群下拉框
    vpc = '[placeholder="请选择网络"]'
    subnet = '[placeholder="请选择子网"]'
    net_type = '.el-form-item:nth-child(2) .el-select__caret'   # CCE容器网络模型下拉框
    volume_type = '(//input[@type="text"])[29]'   # 云硬盘类型下拉框
    volume_size = '(//input[@type="text"])[30]'   # 云硬盘大小下拉框
    os_type = '(//input[@type="text"])[31]'   # 操作系统下拉框
    flavor = '.el-table__fixed-body-wrapper .el-table__row:nth-child(1) .el-radio__inner'  # 规格单选框

    # 列表页面元素

