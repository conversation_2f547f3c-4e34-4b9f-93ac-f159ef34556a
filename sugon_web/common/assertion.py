from playwright.sync_api import Page, Locator, expect


def assert_popup_text(locator: Locator, text, timeout=5000):
    expect(locator).to_contain_text(text)
    expect(locator).not_to_be_visible(timeout=timeout)


def assert_status(page: Page, name: str, status='运行中', timeout=300000, target_column=0):
    """
    验证页面表格中指定资源的状态是否符合预期。
    """
    target_row = page.locator("tbody").locator(f"tr:has-text('{name}')")   # todo: name无法保证唯一匹配行

    # 检查行是否存在（避免无匹配）
    # if not target_row.is_visible(timeout=1000):
    #     raise AssertionError(f"未找到名称为 '{name}' 的资源行")
    
    if target_column:
        column = target_row.locator(f"td:nth-child({target_column})")
        expect(column).to_contain_text(status, timeout=timeout)  # 状态列文本断言（更精确） 
    else:
        expect(target_row).to_contain_text(status, timeout=timeout)


def assert_table_contain(page, keyword, name_column_index=2):
    """
    断言页面表格中某一列至少有一个元素包含指定关键字
    """

    tbody = page.locator("tbody")
    expect(tbody).to_be_visible(timeout=10000)
    result_rows = tbody.locator("tr")
    total_rows = result_rows.count()
    actual_names = []
    if total_rows > 0:
        for row in result_rows.all():
            name_cell = row.locator(f"td:nth-child({name_column_index})")
            name = name_cell.inner_text().strip()
            actual_names.append(name)

    matched = any(keyword in name for name in actual_names)
    assert matched, f"未找到包含关键字 '{keyword}' 的名称，实际名称列表: {actual_names}"


def assert_table_not_contain(page, keyword, name_column_index=2):
    """
    断言页面表格中某一列所有元素都不包含指定关键字，或表格为空
    """

    tbody = page.locator("tbody")
    expect(tbody).to_be_visible(timeout=10000)
    result_rows = tbody.locator("tr")
    total_rows = result_rows.count()
    actual_names = []
    if total_rows > 0:
        for row in result_rows.all():
            name_cell = row.locator(f"td:nth-child({name_column_index})")
            name = name_cell.inner_text().strip()
            actual_names.append(name)

    matched = any(keyword in name for name in actual_names)
    assert not matched, f"期望没有包含关键字 '{keyword}' 的名称，但实际名称列表: {actual_names}"
