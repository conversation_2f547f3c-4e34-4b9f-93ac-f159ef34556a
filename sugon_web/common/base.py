from playwright.sync_api import Page
from sugon_web.common.elements import CommonPage, NavigationConfig
from sugon_web.common.logger import logger


def _format_selector(selector):
    """格式化选择器，自动处理文本选择器的引号

    Args:
        selector: 原始选择器

    Return:
        str: 格式化后的选择器
    """
    # 如果已经有引号，保持原样
    if selector.startswith(('"', "'")):
        return selector

    # 如果是明显的CSS选择器，保持原样
    if selector.startswith(('#', '.', '[', '(')) or '//' in selector:
        return selector

    # 其他情况当作文本处理，添加双引号
    return f'"{selector}"'


class Base:

    def __init__(self, page: Page, env: dict):
        self.page = page
        self.env = env  # 接收环境配置
        self.logger = logger  # 传递日志工具

    @property
    def base_url(self):
        """动态获取当前环境的base_url"""
        return self.env["url"]

    def locator(self, selector):
        """根据选择器定位页面元素"""
        self.logger.debug(f"定位元素: {selector}")
        return self.page.locator(selector)

    def get_by_test_id(self, test_id):
        """通过测试ID定位元素"""
        self.logger.debug(f"通过测试ID定位元素: {test_id}")
        return self.page.get_by_test_id(test_id)

    def get_by_role(self, role, name):
        """通过角色和名称定位元素"""
        self.logger.debug(f"通过角色和名称定位元素: role={role}, name={name}")
        return self.page.get_by_role(role, name=name)

    def get_by_placeholder(self, text):
        """通过占位符定位元素"""
        self.logger.debug(f"通过占位符定位元素: {text}")
        return self.page.get_by_placeholder(text)

    def get_by_label(self, text):
        """通过标签文本定位元素"""
        self.logger.debug(f"通过标签文本定位元素: {text}")
        return self.page.get_by_label(text)

    def get_by_text(self, text):
        """通过文本内容定位元素"""
        self.logger.debug(f"通过文本内容定位元素: {text}")
        return self.page.get_by_text(text)

    def get_by_alt_text(self, text):
        """通过ALT文本定位元素"""
        self.logger.debug(f"通过ALT文本定位元素: {text}")
        return self.page.get_by_alt_text(text)

    def get_by_title(self, text):
        """通过标题定位元素"""
        self.logger.debug(f"通过标题定位元素: {text}")
        return self.page.get_by_title(text)

    def click(self, selector):
        """点击元素，自动处理文本选择器的引号"""
        try:
            original_selector = selector
            formatted_selector = _format_selector(selector)
            result = self.page.click(formatted_selector)
            self.logger.info(f"成功点击元素: {original_selector}")
            return result
        except Exception as e:
            self.logger.error(f"点击元素失败: {original_selector}")
            raise

    def fill(self, selector, value):
        """输入文本"""
        try:
            result = self.page.fill(selector, value)
            self.logger.info(f"成功输入文本: selector={selector}, value={value}")
            return result
        except Exception as e:
            self.logger.error(f"输入文本失败: selector={selector}, value={value}")
            raise

    def hover(self, selector):
        """悬停元素"""
        try:
            original_selector = selector
            formatted_selector = _format_selector(selector)
            result = self.page.hover(formatted_selector)
            self.logger.debug(f"成功悬停元素: {original_selector}")
            return result
        except Exception as e:
            self.logger.error(f"悬停元素失败: {original_selector}")
            raise

    def pop_message(self):
        """页面弹窗元素"""
        self.logger.debug("获取页面弹窗元素")
        return self.locator(CommonPage.message)

    # 公共对话框按钮操作
    def click_confirm(self, timeout=5000):
        """点击确定按钮"""
        try:
            self.get_by_role("button", "确定").click(timeout=timeout)
            return True
        except Exception as e:
            self.logger.warning(f"点击确定按钮失败: {e}")
            return False

    def click_cancel(self, timeout=5000):
        """点击取消按钮"""
        try:
            self.get_by_role("button", "取消").click(timeout=timeout)
            return True
        except Exception as e:
            self.logger.warning(f"点击取消按钮失败: {e}")
            return False

    def close_dialog_if_exists(self, timeout=5000):
        """如果存在对话框，则关闭它"""
        if self.click_confirm(timeout):
            return True
        elif self.click_cancel(timeout):
            return True
        return False

    def wait_for_dialog_and_confirm(self, timeout=10000):
        """等待对话框出现并点击确定"""
        try:
            # 等待确定按钮出现
            confirm_btn = self.get_by_role("button", "确定")
            confirm_btn.wait_for(state="visible", timeout=timeout)
            confirm_btn.click()
            return True
        except Exception as e:
            self.logger.warning(f"等待对话框并确定失败: {e}")
            return False

    def search(self, keyword: str):
        """执行搜索操作"""
        try:
            self.logger.info(f"开始搜索: {keyword}")
            self.locator(CommonPage.ipt_search).fill(keyword)
            self.locator(CommonPage.btn_search).click()
            self.logger.info(f"搜索操作完成: {keyword}")
        except Exception as e:
            self.logger.error(f"搜索操作失败: keyword={keyword}")
            raise


    def navigate_to_service(self, service: str):
        """智能导航到指定服务，支持不同层级结构

        Args:
            service: 服务名称，如 '云容器引擎'、'云硬盘'、'物理服务器' 等

        Returns:
            bool: 导航是否成功
        """
        if service not in NavigationConfig.SERVICE_NAVIGATION_MAP:
            self.logger.error(f"未知的服务: {service}，请检查服务名称或更新导航映射表")
            return False

        try:
            navigation_path = NavigationConfig.SERVICE_NAVIGATION_MAP[service]

            if len(navigation_path) == 1:
                # 两层结构：基础设施 -> 服务
                root_menu = navigation_path[0]
                self.hover(root_menu)
                self.click(service)
                self.logger.info(f"成功导航到服务: {root_menu} -> {service}")

            elif len(navigation_path) == 2:
                # 三层结构：资源中心 -> 二级菜单 -> 服务
                root_menu, category = navigation_path
                self.hover(root_menu)
                self.hover(category)
                self.click(service)
                self.logger.info(f"成功导航到服务: {root_menu} -> {category} -> {service}")

            else:
                self.logger.error(f"服务 {service} 的导航路径配置错误: {navigation_path}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"导航到服务 {service} 失败: {e}")
            return False

