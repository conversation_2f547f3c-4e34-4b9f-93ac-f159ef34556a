# Sugon Web 自动化测试项目 - 安装指南

## 环境要求

- Python 3.8 或更高版本
- Windows/Linux/macOS 操作系统

## 安装步骤

### 1. 创建虚拟环境（推荐）

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate
```

### 2. 安装依赖包

#### 方式一：使用 requirements.txt（传统方式）

```bash
pip install -r requirements.txt
```

#### 方式二：使用 pyproject.toml（现代方式，推荐）

```bash
# 安装项目依赖
pip install -e .

# 安装开发依赖（可选）
pip install -e ".[dev]"
```

### 3. 安装 Playwright 浏览器

```bash
# 安装 Playwright 浏览器
playwright install

# 或者安装特定浏览器
playwright install chromium
playwright install firefox
playwright install webkit
```

### 4. 验证安装

```bash
# 检查 pytest 版本
pytest --version

# 检查 playwright 版本
playwright --version

# 运行测试验证
pytest testcase/test_login.py -v
```

## 项目结构说明

```
sugon_web/
├── common/           # 公共模块
│   ├── base.py      # 基础类
│   ├── elements.py  # 元素定位
│   ├── logger.py    # 日志工具
│   ├── assertion.py # 断言工具
│   └── util.py      # 工具函数
├── pages/           # 页面对象
│   ├── login.py     # 登录页面
│   └── cce.py       # CCE页面
├── testcase/        # 测试用例
│   ├── conftest.py  # pytest配置
│   ├── test_login.py # 登录测试
│   └── test_cce.py  # CCE测试
├── requirements.txt # 依赖包列表（仅包含实际使用的包）
├── pyproject.toml   # 项目配置
└── INSTALL.md       # 安装指南
```

## 常用命令

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest testcase/test_login.py

# 运行特定测试方法
pytest testcase/test_login.py::test_login_error

# 并行运行测试
pytest -n auto

# 生成Allure报告
pytest --alluredir=reports/allure-results
allure serve reports/allure-results
```

### 浏览器相关

```bash
# 指定浏览器类型
pytest --browser-type=firefox

# 有头模式运行（显示浏览器）
pytest --headless=false

# 指定测试环境
pytest --host=************
```

### 代码质量检查（可选）

```bash
# 安装开发依赖
pip install -e ".[dev]"

# 代码格式化
black .

# 代码检查
flake8 .

# 代码质量分析
pylint sugon_web/
```

## 故障排除

### 常见问题

1. **Playwright 浏览器安装失败**
   ```bash
   # 重新安装浏览器
   playwright install --force
   ```

2. **依赖包冲突**
   ```bash
   # 清理并重新安装
   pip uninstall -r requirements.txt -y
   pip install -r requirements.txt
   ```

3. **权限问题**
   ```bash
   # Windows 以管理员身份运行
   # Linux/macOS 使用 sudo
   sudo pip install -r requirements.txt
   ```

### 获取帮助

- 查看 pytest 帮助：`pytest --help`
- 查看 playwright 帮助：`playwright --help`
- 查看项目文档：参考 README.md

## 开发环境配置

### IDE 推荐配置

1. **VS Code**
   - 安装 Python 扩展
   - 安装 Pytest 扩展
   - 配置 Python 解释器为虚拟环境

2. **PyCharm**
   - 配置项目解释器为虚拟环境
   - 安装 pytest 插件
   - 配置运行配置

### 代码风格

项目使用以下工具保持代码质量：
- **Black**: 代码格式化
- **Flake8**: 代码检查
- **Pylint**: 代码质量分析

建议在提交代码前运行：
```bash
black .
flake8 .
pylint sugon_web/
``` 