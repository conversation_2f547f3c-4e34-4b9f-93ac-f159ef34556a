import re
from playwright.sync_api import Page, expect


def assert_popup_success(page: Page, text=None, timeout=5000):
    """根据弹窗文本和类型，断言操作成功
    
    Args:
        page: 页面对象
        text: 期望的弹窗文本内容（可选）
        timeout: 超时时间
    """
    popup = page.locator('.el-message__content')
    expect(popup).to_be_visible(timeout=timeout)

    # 获取弹窗文本
    popup_text = popup.inner_text().strip()

    # 通过检查元素的CSS类名判断弹窗类型
    is_success = popup.evaluate("element => element.parentElement.classList.contains('el-message--success')")
    
    if not is_success:
        raise AssertionError(f"预期操作成功，但实际失败。弹窗文本: {popup_text}")
    
    if text:
        expect(popup).to_contain_text(text)
    
    expect(popup).not_to_be_visible(timeout=timeout)


def assert_popup_error(page: Page, text=None, timeout=5000):
    """根据弹窗文本和类型，断言操作失败
    
    Args:
        page: 页面对象
        text: 期望的弹窗文本内容（可选）
        timeout: 超时时间
    """
    popup = page.locator('.el-message__content')
    expect(popup).to_be_visible(timeout=timeout)

    # 获取弹窗文本
    popup_text = popup.inner_text().strip()

    # 通过检查元素的CSS类名判断弹窗类型
    is_error = popup.evaluate("element => element.parentElement.classList.contains('el-message--error')")
    
    if not is_error:
        raise AssertionError(f"预期操作失败，但实际成功或其他状态。弹窗文本: {popup_text}")
    
    if text:
        expect(popup).to_contain_text(text)
    
    expect(popup).not_to_be_visible(timeout=timeout)


def assert_status(page: Page, name: str, status='运行中', timeout=30000, name_column=2, target_column=0):
    """
    验证页面表格中指定资源的状态是否符合预期。
    
    Args:
        page: 页面对象
        name: 资源名称
        status: 期望状态
        timeout: 超时时间
        name_column: 名称所在列（从1开始）
        target_column: 状态所在列（0表示整行匹配）
    """

    # 使用 getByRole 精确匹配行名称
    target_row = page.get_by_role("row", name=re.compile(rf"^{re.escape(name)}\s"))

    # 检查行是否存在，避免后续长时间等待
    try:
        expect(target_row).to_be_visible(timeout=5000)  # 短超时检查存在性
    except Exception:
        raise AssertionError(f"未找到名称为 '{name}' 的资源行")

    if target_column:
        column = target_row.locator(f"td:nth-child({target_column})")
        expect(column).to_contain_text(status, timeout=timeout)
    else:
        expect(target_row).to_contain_text(status, timeout=timeout)


def assert_table_contain(page, keyword, name_column_index=2):
    """
    断言页面表格中某一列至少有一个元素包含指定关键字
    """

    # 先获取所有行
    rows = page.locator("tbody tr")

    # 获取每行的资源名称
    actual_names = []
    if rows.count() > 0:
        for row in rows.all():
            name_cell = row.locator(f"td:nth-child({name_column_index})")
            name = name_cell.inner_text().strip()
            actual_names.append(name)

    matched = any(keyword in name for name in actual_names)
    assert matched, f"未找到包含关键字 '{keyword}' 的名称，实际名称列表: {actual_names}"
