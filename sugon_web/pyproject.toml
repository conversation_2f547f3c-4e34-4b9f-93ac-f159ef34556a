[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "sugon-web-automation"
version = "1.0.0"
description = "Web自动化测试项目 - 基于Playwright和pytest"
authors = [
    {name = "Sugon Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Testing",
    "Topic :: Software Development :: Testing :: Acceptance",
]

dependencies = [
    "pytest>=7.4.0",
    "playwright>=1.40.0",
    "allure-pytest>=2.13.0",
]

[project.optional-dependencies]
dev = [
    "black>=23.0.0",
    "flake8>=6.0.0",
    "pylint>=2.17.0",
]

[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-v",
    "--tb=short",
    "--strict-markers",
    "--strict-config",
    "--html=reports/report.html",
    "--self-contained-html",
]
testpaths = ["testcase"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "smoke: marks tests as smoke tests",
    "regression: marks tests as regression tests",
    "ui: marks tests as UI tests",
    "api: marks tests as API tests",
]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    ".pytest_cache",
]

[tool.pylint.messages_control]
disable = [
    "C0114",  # missing-module-docstring
    "C0115",  # missing-class-docstring
    "C0116",  # missing-function-docstring
    "R0903",  # too-few-public-methods
    "R0913",  # too-many-arguments
]

[tool.pylint.format]
max-line-length = 88

[tool.pylint.design]
max-args = 10
max-attributes = 10
max-bool-expr = 5
max-branches = 15
max-locals = 20
max-parents = 7
max-public-methods = 20
max-returns = 6
max-statements = 50

[project.urls]
Homepage = "https://github.com/sugon/sugon-web-automation"
Repository = "https://github.com/sugon/sugon-web-automation.git"
Issues = "https://github.com/sugon/sugon-web-automation/issues" 