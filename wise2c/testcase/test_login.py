import pytest
import yaml
from wise2c.api.workflow import User
from wise2c.api.common import get_file_abspath


class TestLogin:

    @classmethod
    def setup_class(cls):
        cls.t = User()

    @pytest.mark.parametrize('username', ['test', '<EMAIL>'])
    @pytest.mark.parametrize('password', ['Wise2c2019'])
    def test_login_success(self, username, password):
        self.t.user_login(username, password)
        self.t.request_should_succeed()

    @pytest.mark.parametrize('username,password',
                             [('wrong', 'Wise2c2019'), ('admin', 'wise2c2019'), ('wrong', 'wise2c2019')])
    def test_login_failed(self, username, password):
        self.t.user_login(username, password)
        self.t.request_should_fail("用户名或密码错误")


class TestUser:

    @classmethod
    def setup_class(cls):
        cls.t = User()

    @pytest.mark.parametrize('username,email,pwd,repwd,status,error_msg', yaml.safe_load(open(
        get_file_abspath("create_user_data.yaml"))))
    def test_create_user(self, username, email, pwd, repwd, status, error_msg):
        r = self.t.user_create(username, email, pwd, repwd)
        print(r['body'])
        assert r['body']['status'] == status
        # if status:
        #     self.t.user_delete(self.t.output("$..id"))
        # else:
        #     assert r['body']['errorMsg'] == error_msg

    def test_get_user(self):
        self.t.user_get()
        self.t.output()

    def test_edit_user(self):
        # self.t.user_edit(45,"mat","<EMAIL>",'test@passw0rd','test@passw0rd')
        # self.t.output()
        self.t.user_approve(45)
        self.t.output()

