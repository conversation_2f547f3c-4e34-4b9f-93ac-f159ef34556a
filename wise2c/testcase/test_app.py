from wise2c.api.orchestration import App


class TestApp:
    @classmethod
    def setup_class(cls):
        cls.t = App()

    def test_create_app(self):
        self.t.create_app('aaay11', desc='abc', istio=True)
        uuid = self.t.output("$..uuid")
        self.t.delete_app(uuid)

    def test_create_app_with_img(self):
        r = self.t.img_upload()
        uuid = r.json()['data']['uuid']
        self.t.create_app('aaa', desc='abc', icon=uuid)
        uuid = self.t.output("$..uuid")
        self.t.delete_app(uuid)
