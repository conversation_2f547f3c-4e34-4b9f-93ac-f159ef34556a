import requests
from wise2c.api.common import Common
from wise2c.api.common import get_file_abspath, get_env


class App(Common):

    def img_upload(self):
        uri = '/orchestration/api/1/application/icon/upload'
        path = get_file_abspath("blue.jpg")
        files = {'file': open(path, 'rb').read()}
        # r = self.post(uri, body=files)   # REST不支持非json API请求,只能用request库
        r = requests.post(get_env()['url']+uri, files=files)
        return r

    def create_app(self, name, desc="", icon="", istio=False):
        uri = '/orchestration/api/1/application'
        body = {"name": name, "description": desc, "icon": "fa-print", "customIcon": icon, "istio": istio}
        r = self.post(uri, body)
        return r

    def get_app(self, page=1, size=10):
        uri = '/orchestration/api/1/application'
        params = '?currentPage={}&pageSize={}'.format(page, size)
        r = self.get(uri, params)
        return r

    def delete_app(self, uuid):
        uri = '/orchestration/api/1/application/' + str(uuid)
        r = self.delete(uri)
        return r

    def app_webshell(self, data):
        uri = "/orchestration_ws/ws/1/env/9c8a8d01-f9d7-4b67-8e3e-cd8832074c92/pod/ingester-8465544c9d-bb4n9/container/ingester/execute?stack_name=tuad2&cols=121&rows=23"
        ws = self.ws_send(uri, data)
        ws.close()
