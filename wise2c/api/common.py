import yaml
import string
from jsonpath import jsonpath
from websocket import create_connection
from REST import REST
import os
import random
from time import strftime
from datetime import datetime, timedelta


class Common(REST):

    """__new__方法，用户单例模式的开关"""
    __instance = None

    def __new__(cls):
        if not cls.__instance:
            cls.__instance = REST.__new__(cls)
        return cls.__instance

    def __init__(self):
        """
        问题：登录的case会携带已有token去发起登录请求，模拟不了真实场景
        """
        env = get_env()
        REST.__init__(self, url=env["url"])
        # token = self.__get_token(env['username'], env['password'])
        # self.set_headers(headers={"Authorization": "Bearer " + token, "Proxy-Connection": "keep-alive"})
        self.set_headers(headers={"Authorization": "Token 6733H7IEJGIFJII477I71H0EIG4I6F7E", "Proxy-Connection": "keep-alive"})

    def __get_token(self, username, password):
        uri = '/api/users/login'
        body = {"username": username, "password": password, "ldapId": "", "type": "IDENTITY"}
        r = self.post(uri, body)
        token = r['body']["data"]['token']
        print(f"用户初始化设置Token: {token}")
        return token

    # ws协议的消息发送
    def ws_send(self, uri, data):
        self.url = self.url.replace("http", "ws")
        conn = create_connection(self.url + uri)
        conn.send(data)
        print("发送消息：{}".format(data))
        resp = conn.recv()
        print("收到消息：{}".format(str(resp, encoding="utf-8")))
        print(conn.headers)
        return conn

    def request_should_succeed(self):
        assert self.output("$..status") == 1

    def request_should_fail(self, error_msg=None):
        assert self.output("$..status") == 0
        assert self.output("$..errorMsg") == error_msg


def get_env():
    """
    从env.yaml读取自动化测试环境的信息(比如url、user、password等)
    :return: 返回环境信息
    """
    abspath = get_file_abspath("env.yaml")
    env = yaml.safe_load(open(abspath))
    return jsonpath(env, "$.{}".format(env['default']))[0]


# 默认在wise2c目录下遍历查找文件，并返回文件的abspath
def get_file_abspath(name):
    current_dir = os.path.dirname(__file__)
    father_dir = os.path.dirname(current_dir)
    for dirpath, dirname, filenames in os.walk(father_dir):
        if name in filenames:
            return os.path.join(dirpath, name)


def random_name(size=5, chars=string.digits):
    prefix = 'autotest-'
    return prefix + ''.join(random.choice(chars) for __ in range(size))


def get_query_time(days):
    end_time = strftime("%Y-%m-%d+%H:%M:%S")
    start_time = datetime.now() + timedelta(days=days)
    start_time = start_time.strftime("%Y-%m-%d+%H:%M:%S")
    return start_time, end_time

