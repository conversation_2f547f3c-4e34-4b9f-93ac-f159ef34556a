from wise2c.api.common import Common


class User(Common):

    def user_login(self, username, password):
        uri = '/api/users/login'
        body = {"username": username, "password": password, "ldapId": "", "type": "IDENTITY"}
        return self.post(uri, body)

    def user_create(self, nickname, email, password, repassword, is_admin=True):
        uri = '/api/users'
        body = {"nickname": nickname, "email": email, "password": password, "rePassword": repassword, "admin": is_admin}
        return self.post(uri, body)

    def user_get(self, name=None):
        uri = f"/api/users?requestPage=1&pageSize=10&page=true"
        if name:
            return self.get(uri, query={"keyword": name})
        else:
            return self.get(uri)

    def user_edit(self, user_id, nickname, email, password, repassword, is_admin=False):
        uri = '/api/users/' + str(user_id)
        body = {"nickname": nickname, "email": email, "password": password, "rePassword": repassword, "admin": is_admin,
                "type": "IDENTITY", "typeId": None, "typeName": None}
        return self.put(uri, body)

    def user_delete(self, user_id):
        uri = '/api/users/' + str(user_id)
        return self.delete(uri)

    def user_approve(self, user_id, enable=True):
        uri = f'/api/users/{str(user_id)}/approve?enable={enable}'
        return self.put(uri)

